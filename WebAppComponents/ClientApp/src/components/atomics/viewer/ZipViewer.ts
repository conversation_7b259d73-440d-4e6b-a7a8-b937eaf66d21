import { css, html } from 'lit'
import * as styles from '@/shared/component-styles.ts'
import { fontAwesome } from '@/shared/font-awesome.ts'
import CommunicationServiceProvider, { CommunicationResponseType } from '@/services/communication-service'
import type { Viewer } from '@/components/atomics/viewer/Viewer.ts'

export type ZipEntry = {
	path: string
	name: string
	uncompressedSize: number
	compressedSize: number
	isDirectory: boolean
}

export type ZipContents = {
	entries: ZipEntry[]
	isPasswordProtected: boolean
	fileName?: string
}

export type FileTreeNode = {
	name: string
	path: string
	isDirectory: boolean
	uncompressedSize?: number
	compressedSize?: number
	children: Map<string, FileTreeNode>
	expanded: boolean
	level: number
}

export class ZipViewer {
	static styles = [
		styles.base,
		styles.color,
		styles.vanishingScrollbar,
		fontAwesome,
		css`
			:host {
				display: block;
				width: 100%;
				height: 100%;
				background: var(--cp-clr-background-lvl-0);
			}

			.zip-container {
				height: 100%;
				display: flex;
				background: var(--cp-clr-background-lvl-0);
				flex-direction: column;
				position: relative;
			}

			.zip-header {
				height: var(--cp-header-height);
				padding: 0 var(--size-spacing-m);
				border-bottom: 0.063rem solid var(--cp-clr-border-weak);
				background: var(--cp-clr-background-lvl-0);
				display: flex;
				justify-content: space-between;
				align-items: center;
			}

			.zip-title {
				font-family: 'Roboto';
				font-size: 1.6rem;
				font-weight: 400;
				margin: 0;
				display: flex;
				align-items: center;
				gap: var(--size-spacing-s);
				padding-left: 1rem;
				white-space: nowrap;
				overflow: hidden;
				text-overflow: ellipsis
			}

			.zip-actions {
				display: flex;
				gap: var(--size-spacing-s);
				align-items: center;
			}


			.zip-content {
				flex: 1;
				overflow: auto;
				padding: 1rem;
			}

			.tree-item {
				display: flex;
				align-items: center;
				padding: var(--size-spacing-s);
				padding-left: 1rem;
				border-bottom: 0.063rem solid var(--cp-clr-border-weak);
				cursor: pointer;
				transition: background-color 0.15s;
				min-height: var(--cp-header-height);
				box-sizing: border-box;
			}

			.tree-item:hover {
				background-color: var(--cp-clr-background-lvl-1);
			}

			.tree-item.folder {
				border-bottom: 0.125rem solid var(--cp-clr-border-weak);
			}

			.tree-indent {
				width: var(--size-spacing-l);
				display: flex;
				justify-content: center;
				align-items: center;
			}

			.tree-indent.nested {
				width: calc(1.875rem);
			}

			.tree-chevron-space {
				width: var(--size-spacing-m);
				height: var(--size-spacing-m);
				display: flex;
				justify-content: center;
				align-items: center;
				margin-left: var(--size-spacing-m);
				margin-right: var(--size-spacing-s);
			}

			.tree-chevron-space {
				width: var(--size-spacing-m);
				height: var(--size-spacing-m);
				display: flex;
				justify-content: center;
				align-items: center;
				margin-left: var(--size-spacing-m);
				margin-right: var(--size-spacing-s);
			}

			.expand-arrow {
				width: 0;
				height: 1.875rem;
				color: var(--cp-clr-text-secondary);
				transition: transform 0.2s;
				cursor: pointer;
				margin-left: 0;
				margin-right: var(--size-spacing-xs);
				transform-origin: center;
				border-radius: var(--size-radius-xs);
				display: flex;
				align-items: center;
				justify-content: center;
			}

			.expand-arrow.expanded {
				transform: rotate(90deg);
			}

			.expand-spacer {
				width: 2rem;
				height: 1.875rem;
			}

			.tree-icon {
				width: 1.875rem;
				height: 1.875rem;
				margin-right: 0;
				margin-left: 0;
			}

			.folder-icon {
				color: var(--cp-clr-signal-attention);
				font-size: 1.875rem;
			}

			.file-icon {
				color: var(--clr-blue-600);
				font-size: 1.875rem;
			}

			.tree-content {
				flex: 1;
				display: flex;
				justify-content: space-between;
				align-items: center;
				min-width: 0;
			}

			.tree-name {
				color: var(--cp-clr-text-primary-default);
				font-size: var(--size-text-m);
				overflow: hidden;
				margin-left: 1.375rem;
				font-family: 'Roboto';
				text-overflow: ellipsis;
				white-space: nowrap;
			}

			.tree-name.expanded-folder {
				font-weight: bold;
			}

			.tree-info {
				font-size: var(--size-text-xs);
				margin-left: var(--size-spacing-m);
				flex-shrink: 0;
				display: flex;
				align-items: center;
				height: 100%;
				min-height: var(--size-spacing-l);
			}

			.object-count {
				padding: var(--size-spacing-xxs) var(--size-spacing-s);
				font-size: var(--size-text-m);
				margin-left: -0.125rem;
			}

			.file-size {
				font-family: Roboto;
				margin-right: 0.215rem;
				font-size: var(--size-text-m);
				color: var(--cp-clr-text-secondary);
			}

			.action-buttons {
				display: flex;
				gap: var(--size-spacing-xs);
				margin-left: var(--size-spacing-s);
				opacity: 0;
				transition: opacity 0.15s;
				align-items: center;
				height: 100%;
			}

			.tree-item:hover .action-buttons {
				opacity: 1;
			}

			.action-btn {
				background: none;
				border: none;
				padding: var(--size-spacing-xs);
				border-radius: var(--size-radius-s);
				cursor: pointer;
				color: var(--cp-clr-text-secondary);
				transition: all 0.15s;
			}

			.action-btn:hover {
				background: var(--cp-clr-background-lvl-1);
				color: var(--cp-clr-text-primary-default);
			}

			.action-btn i {
				font-size: var(--size-text-xs);
			}

			.loading {
				display: flex;
				align-items: center;
				justify-content: center;
				height: 100%;
				color: var(--cp-clr-text-secondary);
			}

			.error {
				display: flex;
				align-items: center;
				justify-content: center;
				height: 100%;
				color: var(--cp-clr-signal-error);
				text-align: center;
				padding: var(--size-spacing-xl);
			}

			.empty {
				display: flex;
				flex-direction: column;
				align-items: center;
				justify-content: center;
				height: 100%;
				color: var(--cp-clr-text-secondary);
				gap: var(--size-spacing-m);
			}

			.password-dialog {
				position: fixed;
				top: 0;
				left: 0;
				right: 0;
				bottom: 0;
				background: rgba(0, 0, 0, 0.5);
				display: flex;
				align-items: center;
				justify-content: center;
				z-index: 1000;
			}

			.password-modal {
				background: var(--cp-clr-background-lvl-0);
				border-radius: var(--size-radius-l);
				padding: var(--size-spacing-xl);
				box-shadow: 0 0.25rem 0.75rem rgba(0, 0, 0, 0.2);
				max-width: 25rem;
				width: 90%;
			}

			.password-modal h3 {
				margin: 0 0 var(--size-spacing-m) 0;
				color: var(--cp-clr-text-primary-default);
				display: flex;
				align-items: center;
				gap: var(--size-spacing-s);
				justify-content: space-between;
			}

			.close-btn {
				background: none;
				border: none;
				cursor: pointer;
				color: var(--cp-clr-text-secondary);
				font-size: var(--size-text-m);
				padding: var(--size-spacing-xs);
				border-radius: var(--size-radius-s);
				transition: color 0.15s;
			}

			.close-btn:hover {
				color: var(--cp-clr-text-primary-default);
			}

			.password-modal {
				color: var(--cp-clr-signal-attention);
				font-size: var(--size-text-m);
			}

			.lock-icon {
				color: var(--cp-clr-signal-attention);
				font-size: var(--size-text-m);
			}

			.password-input {
				width: 100%;
				padding: var(--size-spacing-m);
				border: 0.063 solid var(--cp-clr-border-medium);
				border-radius: var(--size-radius-s);
				font-size: var(--size-text-s);
				margin-bottom: var(--size-spacing-m);
			}

			.password-input:focus {
				outline: none;
				border-color: var(--cp-clr-state-active);
				box-shadow: 0 0 0 0.2rem var(--cp-clr-state-focus);
			}

			.password-buttons {
				display: flex;
				gap: var(--size-spacing-s);
				justify-content: flex-end;
			}

			.password-btn {
				padding: var(--size-spacing-s) var(--size-spacing-m);
				border: none;
				border-radius: var(--size-radius-s);
				cursor: pointer;
				font-size: var(--size-text-xs);
				transition: background-color 0.15s;
			}

			.password-btn.primary {
				background: var(--cp-clr-state-active);
				color: var(--cp-clr-text-primary-inverted);
			}

			.password-btn.primary:hover {
				background: var(--cp-clr-state-active-hover);
			}

			.password-btn.secondary {
				background: var(--cp-clr-text-secondary);
				color: var(--cp-clr-text-primary-inverted);
			}

			.password-btn.secondary:hover {
				background: var(--cp-clr-state-inactive-hover);
			}

			.password-btn:disabled {
				opacity: 0.6;
				cursor: not-allowed;
			}

			.password-btn:disabled:hover {
				background: inherit;
			}

			.password-input:disabled {
				opacity: 0.6;
				cursor: not-allowed;
			}

			.zip-title .lock-icon {
				color: var(--cp-clr-signal-attention);
				margin-left: var(--size-spacing-s);
			}

			.preview-modal {
				position: absolute;
				top: 0;
				left: 0;
				right: 0;
				bottom: 0;
				background: var(--cp-clr-background-dialog);
				display: flex;
				align-items: center;
				justify-content: center;
				z-index: 1000;
				padding: 1rem;
			}

			.preview-container {
				background: var(--cp-clr-background-lvl-0);
				border-radius: var(--size-radius-l);
				width: 100%;
				height: 100%;
				display: flex;
				flex-direction: column;
				box-shadow: 0 0.25rem 1.25rem rgba(0, 0, 0, 0.3);
				overflow: hidden;
			}

			.preview-header {
				display: flex;
				align-items: center;
				background-color: var(--clr-transparent-white-35);
				border-bottom: 0.063rem solid var(--cp-clr-border-medium);
				padding: var(--size-spacing-s) var(--size-spacing-m);
				min-height: var(--cp-header-height);
				gap: var(--size-spacing-m);
				box-shadow: 0 0 0.2rem 0 var(--cp-clr-shadow-weak);
				z-index: 1;
			}

			.preview-header.pdf-header {
				background-color: var(--cp-clr-background-viewer);
				color: var(--cp-clr-background-viewer);
				border-bottom: 0.063rem solid var(--cp-clr-background-viewer);
			}

			.preview-header.pdf-header .preview-toolbar-group lvl-button {
				--button-color: var(--cp-clr-text-primary-inverted);
				--lvl-button-text-color: var(--cp-clr-text-primary-inverted);
				color: var(--cp-clr-text-primary-inverted);
			}

			.preview-header.pdf-header .preview-toolbar-group .pdf-close-button {
				--button-color: black;
				--lvl-button-text-color: black;
				color: black;
			}
			
			.preview-toolbar-group {
				display: flex;
				align-items: center;
				gap: var(--size-spacing-s);
			}
			
			.preview-toolbar-separator {
				width: 0.063rem;
				height: var(--size-spacing-xl);
				background-color: var(--cp-clr-border-medium);
				margin: 0 var(--size-spacing-m);
			}
			
			& [data-dropdown="previewZoomDropdown"] {
				min-width: 7rem;
			}

			.preview-footer {
				padding: var(--size-spacing-m) var(--size-spacing-l);
				border-top: 0.063rem solid var(--cp-clr-background-viewer);
				display: flex;
				justify-content: flex-start;
				align-items: center;
				background: var(--cp-clr-background-viewer);
			}

			.preview-title {
				font-size: var(--size-text-s);
				color: var(--cp-clr-text-primary-default);
				margin: 0;
				display: flex;
				align-items: center;
				gap: var(--size-spacing-s);
			}


			.preview-content {
				flex: 1;
				padding: 0.5rem;
				overflow: auto;
				display: flex;
				align-items: center;
				justify-content: center;
				background: var(--cp-clr-background-viewer);
				position: relative;
			}

			.preview-image {
				max-width: 100%;
				max-height: 100%;
				object-fit: contain;
				border-radius: var(--size-radius-s);
			}

			.preview-text {
				width: 100%;
				height: 100%;
				white-space: pre-wrap;
				font-family: monospace;
				font-size: var(--size-text-xs);
				color: var(--cp-clr-text-primary-default);
				background: var(--cp-clr-background-lvl-0);
				padding: 1rem;
				border: 0.063 solid var(--cp-clr-border-weak);
				border-radius: var(--size-radius-s);
				overflow: auto;
			}

			.preview-unsupported {
				display: flex;
				align-items: center;
				justify-content: center;
				height: 100%;
				color: var(--cp-clr-text-secondary);
			}

			.preview-unsupported-container {
				text-align: center;
				max-width: 25rem;
				width: 100%;
				padding: var(--size-spacing-xl);
			}

			.preview-unsupported-icon {
				font-size: var(--size-text-xxxl);
				margin-bottom: var(--size-spacing-l);
				color: var(--cp-clr-text-tertiary);
			}

			.preview-unsupported-title {
				margin: 0 0 var(--size-spacing-m) 0;
				color: var(--cp-clr-text-tertiary);
				font-size: var(--size-text-l);
			}

			.preview-unsupported-filename {
				margin: 0 0 var(--size-spacing-l) 0;
				color: var(--cp-clr-text-tertiary);
				font-size: var(--size-text-l);
				word-break: break-all;
			}

			.preview-nav-arrow {
				position: absolute;
				top: 50%;
				transform: translateY(-50%);
				width: 3rem;
				height: 5rem;
				background: transparent;
				color: white;
				border: none;
				border-radius: 0.5rem;
				cursor: pointer;
				display: flex;
				align-items: center;
				justify-content: center;
				font-size: 1.5625rem;
				font-weight: 100;
				transition: all 0.2s ease;
				opacity: 0;
				z-index: 10;
			}
			
			.preview-content:has(.preview-image) .preview-nav-arrow {
				color: white;
			}

			/* Default state - visible on hover */
			.preview-content:hover .preview-nav-arrow {
				opacity: 0.8;
			}

			/* Hover state */
			.preview-nav-arrow:hover {
				background: var(--cp-clr-hover-grey);
				opacity: 1;
				transform: translateY(-50%) scale(1.05);
			}

			/* Pressed/Active state */
			.preview-nav-arrow:active {
				background: var(--clr-transparent-black-50);
				transform: translateY(-50%) scale(0.95);
				transition: all 0.1s ease;
			}

			/* Focused state */
			.preview-nav-arrow:focus {
				outline: 0.125rem solid var(--cp-clr-state-focus);
				outline-offset: 0.1rem;
				background: var(--cp-clr-hover-grey);
				opacity: 1;
			}

			/* Focused + Pressed state */
			.preview-nav-arrow:focus:active {
				outline: 0.125rem solid rgba(255, 255, 255, 0.8);
				background: rgba(0, 0, 0, 0.9);
				transform: translateY(-50%) scale(0.95);
			}

			/* Disabled state */
			.preview-nav-arrow:disabled {
				opacity: 0;
				cursor: not-allowed;
				background: rgba(0, 0, 0, 0.3);
			}

			.preview-nav-arrow:disabled:hover {
				transform: translateY(-50%);
				opacity: 0;
			}

			.preview-content:hover .preview-nav-arrow:disabled {
				opacity: 0.2;
			}

			.preview-nav-arrow.left {
				left: var(--size-spacing-m);
			}

			.preview-nav-arrow.right {
				right: var(--size-spacing-m);
			}

			/* Password UI Styles */
			.password-content {
				display: flex;
				align-items: center;
				justify-content: center;
				height: 100%;
				color: var(--cp-clr-text-secondary);
			}

			.password-form-container {
				text-align: center;
				max-width: 25rem;
				width: 100%;
				padding: var(--size-spacing-xl);
			}

			.password-lock-icon {
				font-size: var(--size-text-xxxl);
				margin-bottom: var(--size-spacing-l);
				color: var(--cp-clr-text-tertiary);
			}

			.password-title {
				margin: 0 0 var(--size-spacing-l) 0;
				color: var(--cp-clr-text-primary-default);
				font-size: var(--size-text-s);
				font-weight: 400;
				line-height: 1.4;
			}

			.password-input-container {
				margin-bottom: var(--size-spacing-l);
			}

			.password-error {
				color: var(--cp-clr-signal-error);
				font-size: var(--size-text-xs);
				margin-bottom: var(--size-spacing-m);
				text-align: center;
			}

			.password-buttons {
				display: flex;
				justify-content: space-between;
				gap: var(--size-spacing-m);
			}

			.encrypted-message-container {
				text-align: center;
			}

			.encrypted-message-text {
				font-size: var(--size-text-s);
				margin-bottom: var(--size-spacing-l);
				color: var(--cp-clr-text-secondary);
			}

			.password-entry-btn {
				background: var(--cp-clr-state-active);
				border: none;
				color: var(--cp-clr-text-primary-inverted);
				cursor: pointer;
				font-size: var(--size-text-s);
				padding: var(--size-spacing-m) var(--size-spacing-l);
				border-radius: var(--size-radius-s);
				display: flex;
				align-items: center;
				gap: var(--size-spacing-s);
				margin: 0 auto;
				transition: background-color 0.15s;
			}

			.password-entry-btn:hover {
				background-color: var(--cp-clr-state-active-hover);
			}
		`
	]

	private viewer: Viewer
	
	/**
	 * The ID of the data source containing the ZIP file
	 */
	public dataSourceId: string = ''
	
	/**
	 * The unique identifier of the ZIP file
	 */
	public fileId: string = ''
	
	/**
	 * The display name of the ZIP file
	 */
	public filename: string = ''

	private zipContents: ZipContents | null = null
	private collapsedFolders: Set<string> = new Set()
	private loading = false
	private error: string | null = null
	private currentPassword = ''
	private passwordLoading = false
	private showPasswordForm = false
	private showPasswordEntry = false
	private showPassword = false
	private sortDirection: 'asc' | 'desc' = 'asc'
	private showPreview = false
	private previewFile: ZipEntry | null = null
	private previewContent: string | null = null
	private previewLoading = false
	private previewZoom: number = -1
	private previewableFiles: ZipEntry[] = []
	private previewRotation: number = 0 // Rotation in degrees (0, 90, 180, 270)
	private isFullscreen: boolean = false
	private previewFontSize: number = 14 // Font size in pixels for text preview
	
	// Store Office action URL for rendering Office preview
	private officeActionUrl?: string
	
	// Store temp file ID for PDF preview
	private tempFileId?: string
	
	constructor(viewer: Viewer) {
		this.viewer = viewer
	}




	public async loadZipContents(password?: string) {
		if (!this.dataSourceId || !this.fileId) {
			this.error = this.viewer.localize('missingRequiredParameters')
			return
		}

		this.loading = true
		this.error = null
		this.collapsedFolders.clear()

		try {
			const response = await CommunicationServiceProvider.get<ZipContents>(
				`/Api/DataSources/${this.dataSourceId}/Files/${this.fileId}/ZipContents`,
				{ searchParams: password ? { password } : {} }
			)

			if (response.state === CommunicationResponseType.Error) {
				this.error = response.error.errorMessage || this.viewer.localize('failedToLoadArchiveContents')
			} else if (response.state === CommunicationResponseType.Ok) {
				const data = response.data || { entries: [], isPasswordProtected: false }
				this.zipContents = data

				// Update filename from backend response if available
				if (data.fileName && !this.filename) {
					this.filename = data.fileName
				}

				this.initializeCollapsedFolders()

				this.showPasswordForm = data.isPasswordProtected && (!password || data.entries.length === 0)
				this.showPasswordEntry = false

				this.viewer.requestUpdate()
			} else {
				this.error = this.viewer.localize('requestAborted')
			}
		} catch (err) {
			this.error = this.viewer.localize('failedToLoadZipContents')
		} finally {
			this.loading = false
		}
	}

	private initializeCollapsedFolders() {
		if (!this.zipContents) return
		
		const processedDirs = new Set<string>()
		this.zipContents.entries.forEach(entry => {
			const pathParts = entry.path.split('/').filter(p => p.length > 0)
			for (let i = 0; i < pathParts.length - 1; i++) {
				const dirPath = pathParts.slice(0, i + 1).join('/')
				if (!processedDirs.has(dirPath)) {
					processedDirs.add(dirPath)
					this.collapsedFolders.add(dirPath)
				}
			}
		})
	}

	private async downloadEntry(entry: ZipEntry) {
		try {
			// Use the same approach as the entire ZIP download, but create a direct download URL
			// Since there's no specific download endpoint for ZIP entries, we'll use the existing 
			// ZipEntry endpoint and force download behavior with proper headers
			const downloadUrl = this.buildZipEntryDownloadUrl(entry.path, entry.name)
			
			const link = document.createElement('a')
			link.href = downloadUrl
			link.download = entry.name
			// Add target="_blank" to ensure it opens in a new context for download
			link.target = '_blank'
			document.body.appendChild(link)
			link.click()
			document.body.removeChild(link)
		} catch (err) {
			console.error('Download failed:', err)
			// Optionally show user-friendly error message
			this.error = this.viewer.localize('downloadFailed') || 'Download failed'
			setTimeout(() => {
				this.error = null
				this.viewer.requestUpdate()
			}, 3000)
			this.viewer.requestUpdate()
		}
	}
	
	private buildZipEntryDownloadUrl(entryPath: string, filename: string): string {
		// Build URL similar to buildZipEntryUrl but for download
		const baseUrl = `/Api/DataSources/${this.dataSourceId}/Files/${this.fileId}/ZipEntry`
		const params = new URLSearchParams({
			entryPath: entryPath,
			// Add download parameter to suggest download behavior
			download: 'true',
			filename: filename
		})
		
		if (this.zipContents?.isPasswordProtected && this.currentPassword) {
			params.append('password', this.currentPassword)
		}
		
		return `${baseUrl}?${params.toString()}`
	}

	private async downloadEntireZip() {
		try {
			const link = document.createElement('a')
			link.href = `/Api/DataSources/${this.dataSourceId}/Files/${this.fileId}/Download`
			link.download = this.filename || 'archive.zip'
			document.body.appendChild(link)
			link.click()
			document.body.removeChild(link)
		} catch (err) {
			console.error('Download failed:', err)
			// Show user-friendly error message
			this.error = this.viewer.localize('downloadFailed') || 'Download failed'
			setTimeout(() => {
				this.error = null
				this.viewer.requestUpdate()
			}, 3000)
			this.viewer.requestUpdate()
		}
	}

	private async previewEntry(entry: ZipEntry) {
		this.previewFile = entry
		this.showPreview = true
		this.previewLoading = true
		this.previewContent = null
		this.previewRotation = 0 // Reset rotation when switching to a different item
		this.previewFontSize = 14 // Reset font size when switching to a different item
		
		// Build a list of previewable files for navigation
		this.buildPreviewableFilesList()
		
		// Force re-render to show the loading state
		this.viewer.requestUpdate()
		
		try {
			// Check if it's a previewable file type
			const fileExtension = entry.name.lastIndexOf('.') > -1 ? entry.name.substring(entry.name.lastIndexOf('.') + 1).toLowerCase() : ''
			const isImage = ['jpg', 'jpeg', 'png', 'gif', 'svg'].includes(fileExtension)
			const isText = ['txt', 'json', 'csv', 'md', 'xml', 'js', 'css', 'html', 'py', 'java', 'cpp', 'c', 'h'].includes(fileExtension)
			const isOfficeFile = ['doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx'].includes(fileExtension)
			const isPdf = fileExtension === 'pdf'

			// If no extension or unsupported extension, show unsupported view
			if (fileExtension === '' || (!isImage && !isText && !isOfficeFile && !isPdf)) {
				this.previewContent = 'unsupported'
			} else if (isOfficeFile) {
				// Handle Office file preview using Microsoft Office Online
				await this.openOfficePreview(entry)
			} else if (isPdf) {
				// Handle PDF file preview using Apryse WebViewer
				await this.openPdfPreview(entry)
			} else if (isImage || isText) {
				const url = this.buildZipEntryUrl(entry.path)
				
				if (isText) {
					// Fetch text content
					const response = await fetch(url)
					if (response.ok) {
						this.previewContent = await response.text()
					} else {
						this.previewContent = this.viewer.localize('failedToLoadFileContent')
					}
				} else {
					// For images, set the URL as content
					this.previewContent = url
				}
			}
		} catch (err) {
			this.previewContent = this.viewer.localize('errorLoadingFile')
		} finally {
			this.previewLoading = false
			this.viewer.requestUpdate()
			// Apply zoom and font size after content loads
			setTimeout(() => {
				this.applyPreviewZoom()
				this.applyPreviewFontSize()
			}, 100)
		}
	}

	private async openOfficePreview(entry: ZipEntry) {
		try {
			// Set up the preview modal for Office content
			this.previewFile = entry
			this.showPreview = true
			this.previewLoading = true
			this.previewContent = null
			
			// Force re-render to show the loading state
			this.viewer.requestUpdate()
			
			const extractUrl = this.buildZipEntryUrl(entry.path)
			
			const extractResponse = await fetch(extractUrl)
			
			if (!extractResponse.ok) {
				this.showPreviewError(this.viewer.localize('failedToExtractOfficeFile'))
				return
			}
			
			// Get the extracted file as a blob
			const fileBlob = await extractResponse.blob()
			
			// Create a temporary file by uploading the blob to the data source
			const formData = new FormData()
			formData.append('file', fileBlob, entry.name)
			
			const uploadUrl = `/Api/DataSources/${this.dataSourceId}/Files`
			const uploadResponse = await fetch(uploadUrl, {
				method: 'POST',
				body: formData
			})
			
			if (!uploadResponse.ok) {
				this.showPreviewError(this.viewer.localize('failedToCreateTempFileOffice'))
				return
			}
			
			const uploadData = await uploadResponse.json()
			const tempFileId = uploadData.fileId || uploadData.data?.fileId
			
			if (!tempFileId) {
				this.showPreviewError(this.viewer.localize('failedToCreateTempFileOffice'))
				return
			}
			
			// Step 2: Get the Office action URL directly
			const officeUrl = `/Api/MicrosoftOffice/${this.dataSourceId}/File/${tempFileId}/ViewMode/view`
			
			const officeResponse = await fetch(officeUrl)
			if (!officeResponse.ok) {
				this.showPreviewError(this.viewer.localize('failedToLoadOfficePreview'))
				return
			}
			
			const officeData = await officeResponse.json()
			if (!officeData.actionUrl) {
				this.showPreviewError(this.viewer.localize('failedToGetOfficePreviewUrl'))
				return
			}
			
			// Store the Office action URL for rendering
			this.officeActionUrl = officeData.actionUrl
			
			// Switch to Office preview mode
			this.previewContent = 'office'
			this.showPreview = true
			this.previewLoading = false
			
			this.viewer.requestUpdate()
		} catch (err) {
			this.showPreviewError(this.viewer.localize('errorOpeningOfficePreview'))
		}
	}

	private async openPdfPreview(entry: ZipEntry) {
		try {
			// Set up the preview modal for PDF content
			this.previewFile = entry
			this.showPreview = true
			this.previewLoading = true
			this.previewContent = null
			
			// Force re-render to show the loading state
			this.viewer.requestUpdate()
			
			const extractUrl = this.buildZipEntryUrl(entry.path)
			
			const extractResponse = await fetch(extractUrl)
			
			if (!extractResponse.ok) {
				this.showPreviewError(this.viewer.localize('failedToExtractPdfFile'))
				return
			}
			
			// Get the extracted file as a blob
			const fileBlob = await extractResponse.blob()
			
			// Create a temporary file by uploading the blob to the data source
			const formData = new FormData()
			formData.append('file', fileBlob, entry.name)
			
			const uploadUrl = `/Api/DataSources/${this.dataSourceId}/Files`
			const uploadResponse = await fetch(uploadUrl, {
				method: 'POST',
				body: formData
			})
			
			if (!uploadResponse.ok) {
				this.showPreviewError(this.viewer.localize('failedToCreateTempFilePdf'))
				return
			}
			
			const uploadData = await uploadResponse.json()
			const tempFileId = uploadData.fileId || uploadData.data?.fileId
			
			if (!tempFileId) {
				this.showPreviewError(this.viewer.localize('failedToCreateTempFilePdf'))
				return
			}
			
			// Step 2: Store the temp file ID for use in rendering
			this.tempFileId = tempFileId
			
			// Set preview content to PDF mode
			this.previewContent = 'pdf'
			this.previewLoading = false
			
			this.viewer.requestUpdate()
			
			// Step 3: Load the PDF using the nested viewer after DOM is updated
			setTimeout(() => {
				this.loadPdfInNestedViewer()
			}, 100)
		} catch (err) {
			this.showPreviewError(this.viewer.localize('errorOpeningPdfPreview'))
		}
	}

	private loadPdfInNestedViewer() {
		// Find the nested viewer element in the DOM
		const nestedViewerElement = this.viewer.shadowRoot?.querySelector('.zip-pdf-viewer') as any
		if (nestedViewerElement && this.tempFileId) {
			// Configure the viewer similar to GridView pattern
			nestedViewerElement.user = this.viewer.user
			nestedViewerElement.readonly = this.viewer.readonly
			nestedViewerElement.allowFileOptions = false
			nestedViewerElement.showPlaceholder = false
			
			// Load the PDF document
			nestedViewerElement.loadNewDocument(this.dataSourceId, this.tempFileId, 'pdf')
		}
	}

	private showPreviewError(message: string) {
		this.error = message
		this.previewLoading = false
		this.showPreview = false
		this.viewer.requestUpdate()
		
		setTimeout(() => {
			if (this.error === message) {
				this.error = null
				this.viewer.requestUpdate()
			}
		}, 5000)
	}
	
	private closePreview() {
		if (this.previewContent === 'office') this.officeActionUrl = undefined
		if (this.previewContent === 'pdf') this.tempFileId = undefined
		
		Object.assign(this, {
			showPreview: false,
			previewFile: null,
			previewContent: null,
			previewLoading: false,
			previewZoom: -1,
			previewableFiles: [],
			previewRotation: 0,
			isFullscreen: false
		})
		this.viewer.requestUpdate()
	}
	
	private handlePreviewClick = (e: Event) => e.stopPropagation()
	private handleBackdropClick = () => this.closePreview()
	
	private handlePreviewZoom(factor: number) {
		const currentZoom = this.previewZoom < 0 ? 1 : this.previewZoom
		this.previewZoom = Math.round((currentZoom * factor) * 100) / 100
		this.applyPreviewZoom()
		this.viewer.requestUpdate()
	}
	
	private handlePreviewZoomSelect = (zoomLevel: number) => {
		this.previewZoom = zoomLevel < 0 ? -1 : zoomLevel / 100
		this.applyPreviewZoom()
		this.viewer.requestUpdate()
	}
	
	private applyPreviewZoom() {
		// Use requestAnimationFrame to ensure DOM is updated
		requestAnimationFrame(() => {
			const previewImage = this.viewer.shadowRoot?.querySelector('.preview-image') as HTMLImageElement
			if (previewImage) {
				let transform = ''
				
				// Apply rotation
				if (this.previewRotation !== 0) {
					transform += `rotate(${this.previewRotation}deg)`
				}
				
				// Apply zoom
				if (this.previewZoom === -1) {
					// Auto zoom - fit to container
					previewImage.style.width = 'auto'
					previewImage.style.height = 'auto'
					previewImage.style.maxWidth = '100%'
					previewImage.style.maxHeight = '100%'
				} else {
					// Specific zoom level
					previewImage.style.maxWidth = 'none'
					previewImage.style.maxHeight = 'none'
					previewImage.style.width = 'auto'
					previewImage.style.height = 'auto'
					transform += ` scale(${this.previewZoom})`
				}
				
				previewImage.style.transform = transform || 'none'
				previewImage.style.transformOrigin = 'center'
			}
		})
		this.viewer.requestUpdate()
	}
	
	private handleImageWheel = (e: WheelEvent) => {
		e.preventDefault()
		e.stopPropagation()
		
		// Calculate zoom factor based on wheel direction
		const zoomFactor = e.deltaY < 0 ? 1.1 : 0.9
		
		// Get current zoom level
		const currentZoom = this.previewZoom < 0 ? 1 : this.previewZoom
		
		// Calculate new zoom level with constraints
		let newZoom = currentZoom * zoomFactor
		
		// Set zoom limits (0.1x to 10x)
		newZoom = Math.max(0.1, Math.min(10, newZoom))
		
		// Round to 2 decimal places for cleaner values
		this.previewZoom = Math.round(newZoom * 100) / 100
		
		this.applyPreviewZoom()
		this.viewer.requestUpdate()
	}
	
	private handlePreviewFontSizeChange(delta: number) {
		// Change font size with constraints (8px to 48px)
		const newFontSize = Math.max(8, Math.min(48, this.previewFontSize + delta))
		this.previewFontSize = newFontSize
		this.applyPreviewFontSize()
		this.viewer.requestUpdate()
	}
	
	private applyPreviewFontSize() {
		// Use requestAnimationFrame to ensure DOM is updated
		requestAnimationFrame(() => {
			const previewText = this.viewer.shadowRoot?.querySelector('.preview-text') as HTMLElement
			if (previewText) {
				previewText.style.fontSize = `${this.previewFontSize}px`
			}
		})
	}
	
	private handlePreviewRotate() {
		this.previewRotation = (this.previewRotation + 90) % 360
		this.applyPreviewZoom()
		this.viewer.requestUpdate()
	}
	
	private toggleFullscreen() {
		this.isFullscreen = !this.isFullscreen
		const previewModal = this.viewer.shadowRoot?.querySelector('.preview-modal') as HTMLElement
		if (previewModal) {
			if (this.isFullscreen) {
				previewModal.style.position = 'fixed'
				previewModal.style.top = '0'
				previewModal.style.left = '0'
				previewModal.style.width = '100vw'
				previewModal.style.height = '100vh'
				previewModal.style.zIndex = '9999'
				previewModal.style.padding = '0'
			} else {
				previewModal.style.position = 'absolute'
				previewModal.style.top = '0'
				previewModal.style.left = '0'
				previewModal.style.width = '100%'
				previewModal.style.height = '100%'
				previewModal.style.zIndex = '1000'
				previewModal.style.padding = '1rem'
			}
		}
		this.viewer.requestUpdate()
	}
	
	private buildPreviewableFilesList() {
		if (!this.zipContents) {
			this.previewableFiles = []
			return
		}
		
		// Get the current file's directory path for filtering
		let currentDirectoryPath = ''
		if (this.previewFile) {
			const pathParts = this.previewFile.path.split('/').filter(p => p.length > 0)
			// Remove the filename to get the directory path
			pathParts.pop()
			currentDirectoryPath = pathParts.join('/')
		}
		
		// Get all files (not directories) that are currently visible and in the same directory
		const structure = this.getDirectoryStructure()
		this.previewableFiles = structure
			.filter(item => {
				if (item.isDirectory || !this.shouldShowItem(item)) {
					return false
				}
				
				// Get this item's directory path
				const itemPathParts = item.path.split('/').filter(p => p.length > 0)
				itemPathParts.pop() // Remove filename
				const itemDirectoryPath = itemPathParts.join('/')
				
				// Only include files from the same directory level
				return itemDirectoryPath === currentDirectoryPath
			})
			.map(item => ({
				path: item.path,
				name: item.name,
				uncompressedSize: item.uncompressedSize || 0,
				compressedSize: item.compressedSize || 0,
				isDirectory: false
			}))
	}
	
	private getCurrentFileIndex = () => this.previewFile ? this.previewableFiles.findIndex(file => file.path === this.previewFile!.path) : -1
	
	private navigateToFile(direction: 'prev' | 'next') {
		const currentIndex = this.getCurrentFileIndex()
		if (currentIndex === -1) return
		
		this.closeAllDropdowns()
		
		const newIndex = direction === 'prev' ? currentIndex - 1 : currentIndex + 1
		if (newIndex >= 0 && newIndex < this.previewableFiles.length) {
			this.previewEntry(this.previewableFiles[newIndex])
		}
	}
	
	private canNavigatePrev = () => this.getCurrentFileIndex() > 0
	
	private canNavigateNext = () => {
		const index = this.getCurrentFileIndex()
		return index >= 0 && index < this.previewableFiles.length - 1
	}
	
	private renderPreviewZoomControls() {
		if (!this.previewFile) return ''
		
		// Only show zoom controls for images
		const fileExtension = this.previewFile.name.lastIndexOf('.') > -1 ? this.previewFile.name.substring(this.previewFile.name.lastIndexOf('.') + 1).toLowerCase() : ''
		const isImage = ['jpg', 'jpeg', 'png', 'gif', 'webp', 'svg'].includes(fileExtension)
		
		if (!isImage) return ''
		
		return html`
			<div class="preview-toolbar-group">
				<lvl-button label="${this.previewZoom == -1 ? this.viewer.localize('auto') : Math.round(this.previewZoom * 100) + ' %'}"
										tooltip="${this.viewer.localize('zoom')}"
										tooltip-placement="bottom" data-dropdown="previewZoomDropdown" show-dropdown-anchor></lvl-button>
				<lvl-dropdown name="previewZoomDropdown" placement="bottom-start" offset-block-y="4">
					<lvl-menu>
						<lvl-menu-item @click=${(e: Event) => { e.stopPropagation(); this.handlePreviewZoomSelect(-1) }}>
							Auto
						</lvl-menu-item>
						<lvl-menu-item @click=${(e: Event) => { e.stopPropagation(); this.handlePreviewZoomSelect(25) }}>25%</lvl-menu-item>
						<lvl-menu-item @click=${(e: Event) => { e.stopPropagation(); this.handlePreviewZoomSelect(50) }}>50%</lvl-menu-item>
						<lvl-menu-item @click=${(e: Event) => { e.stopPropagation(); this.handlePreviewZoomSelect(75) }}>75%</lvl-menu-item>
						<lvl-menu-item @click=${(e: Event) => { e.stopPropagation(); this.handlePreviewZoomSelect(100) }}>100%</lvl-menu-item>
						<lvl-menu-item @click=${(e: Event) => { e.stopPropagation(); this.handlePreviewZoomSelect(125) }}>125%</lvl-menu-item>
						<lvl-menu-item @click=${(e: Event) => { e.stopPropagation(); this.handlePreviewZoomSelect(150) }}>150%</lvl-menu-item>
						<lvl-menu-item @click=${(e: Event) => { e.stopPropagation(); this.handlePreviewZoomSelect(200) }}>200%</lvl-menu-item>
					</lvl-menu>
				</lvl-dropdown>
				<lvl-button icon="circle-minus" tooltip="${this.viewer.localize('zoomOut')}" tooltip-placement="bottom" 
										@click=${(e: Event) => { e.stopPropagation(); this.handlePreviewZoom(0.8) }}></lvl-button>
				<lvl-button icon="circle-plus" tooltip="${this.viewer.localize('zoomIn')}" tooltip-placement="bottom" 
										@click=${(e: Event) => { e.stopPropagation(); this.handlePreviewZoom(1.25) }}></lvl-button>
			</div>
		`
	}
	
	private renderPreviewFontControls() {
		if (!this.previewFile) return ''
		
		// Only show font controls for text files
		const fileExtension = this.previewFile.name.lastIndexOf('.') > -1 ? this.previewFile.name.substring(this.previewFile.name.lastIndexOf('.') + 1).toLowerCase() : ''
		const isText = ['txt', 'json', 'csv', 'md', 'xml', 'js', 'css', 'html', 'py', 'java', 'cpp', 'c', 'h', 'ts', 'tsx', 'jsx', 'yaml', 'yml', 'ini', 'cfg', 'conf', 'log'].includes(fileExtension)
		
		if (!isText) return ''
		
		return html`
			<div class="preview-toolbar-group">
				<lvl-button icon="minus" tooltip="${this.viewer.localize('decreaseFontSize')}" tooltip-placement="bottom" 
										?disabled=${this.previewFontSize <= 8}
										@click=${(e: Event) => { e.stopPropagation(); this.handlePreviewFontSizeChange(-2) }}></lvl-button>
				<lvl-button icon="plus" tooltip="${this.viewer.localize('increaseFontSize')}" tooltip-placement="bottom" 
										?disabled=${this.previewFontSize >= 48}
										@click=${(e: Event) => { e.stopPropagation(); this.handlePreviewFontSizeChange(2) }}></lvl-button>
			</div>
		`
	}
	
	private renderPreviewImageControls() {
		if (!this.previewFile) return ''
		
		// Check if current file is an image
		const fileExtension = this.previewFile.name.lastIndexOf('.') > -1 ? this.previewFile.name.substring(this.previewFile.name.lastIndexOf('.') + 1).toLowerCase() : ''
		const isImage = ['jpg', 'jpeg', 'png', 'gif', 'webp', 'svg'].includes(fileExtension)
		
		return html`
			<div class="preview-toolbar-group">
				${isImage ? html`
					<lvl-button icon="rotate-left" 
								tooltip="Rotate image" 
								tooltip-placement="bottom" 
								type="tertiary"
								@click=${(e: Event) => { e.stopPropagation(); this.handlePreviewRotate() }}></lvl-button>
				` : ''}
				<lvl-button icon="${this.isFullscreen ? 'compress' : 'expand'}" 
							type="tertiary" 
							tooltip="${this.isFullscreen ? 'Exit fullscreen' : 'Enter fullscreen'}" 
							tooltip-placement="bottom"
							@click=${(e: Event) => { e.stopPropagation(); this.toggleFullscreen() }}></lvl-button>
			</div>
		`
	}

	private renderPreviewModal() {
		if (!this.showPreview || !this.previewFile) return ''
		
		const isPdfView = this.previewContent === 'pdf'
		
		return html`
			<div class="preview-modal" @click=${this.handleBackdropClick}>
				<div class="preview-container" @click=${this.handlePreviewClick}>
					<div class="preview-header ${isPdfView ? 'pdf-header' : ''}">
						${isPdfView ? html`
							<!-- PDF view: only show the close button -->
							<div style="flex: 1"></div>
							<div class="preview-toolbar-group">
								<lvl-button class="pdf-close-button" icon="xmark" type="tertiary" @click=${(e: Event) => { e.stopPropagation(); this.closePreview() }}></lvl-button>
							</div>
						` : html`
							<!-- Normal view: show all controls -->
							<div class="preview-toolbar-group">
								<lvl-button icon="bars" type="tertiary" data-dropdown="preview-menu-dropdown"></lvl-button>
								<lvl-dropdown name="preview-menu-dropdown" placement="bottom-start" shift arrow>
									<lvl-menu>
										<lvl-menu-item icon-left="arrow-down-to-line" @click=${(e: Event) => { e.stopPropagation(); this.downloadEntry(this.previewFile!) }}>
											Download
										</lvl-menu-item>
									</lvl-menu>
								</lvl-dropdown>
							</div>
							
							${this.renderPreviewZoomControls()}
							
							${this.renderPreviewFontControls()}
							
							${this.renderPreviewImageControls()}
							
							<div style="flex: 1"></div>
							
							<div class="preview-toolbar-group">
								<lvl-button icon="xmark" type="tertiary" @click=${(e: Event) => { e.stopPropagation(); this.closePreview() }}></lvl-button>
							</div>
						`}
					</div>
					<div class="preview-content">
						${this.renderPreviewContent()}
						
						<!-- Navigation arrows -->
						<button class="preview-nav-arrow left" 
										@click=${(e: Event) => { e.stopPropagation(); this.navigateToFile('prev') }}
										?disabled=${!this.canNavigatePrev()}
										title="${this.viewer.localize('previousFile')}">
							<i class="fa-light fa-chevron-left"></i>
						</button>
						
						<button class="preview-nav-arrow right" 
										@click=${(e: Event) => { e.stopPropagation(); this.navigateToFile('next') }}
										?disabled=${!this.canNavigateNext()}
										title="${this.viewer.localize('nextFile')}">
							<i class="fa-light fa-chevron-right"></i>
						</button>
					</div>
					<div class="preview-footer">
						<div class="preview-title">
							<i class="${((filename: string) => {
							const ext = filename.split('.').pop()?.toLowerCase()
							return ext === 'pdf' ? 'far fa-file-pdf' :
								['jpg', 'jpeg', 'png', 'gif', 'webp'].includes(ext || '') ? 'far fa-file-image' :
								['doc', 'docx'].includes(ext || '') ? 'far fa-file-word' :
								['xls', 'xlsx'].includes(ext || '') ? 'far fa-file-excel' :
								['ppt', 'pptx'].includes(ext || '') ? 'far fa-file-powerpoint' :
								ext === 'txt' ? 'far fa-file-lines' :
								['zip', 'rar', '7z', 'tar', 'gz', 'bz2'].includes(ext || '') ? 'far fa-file-zip' : 'far fa-file'
						})(this.previewFile.name)}"></i>
							<span>${this.previewFile.name}</span>
						</div>
					</div>
				</div>
			</div>
		`
	}
	
	private renderPreviewContent() {
		if (this.previewLoading) {
			return html`
				<div style="display: flex; align-items: center; justify-content: center; height: 100%;">
					<i class="fas fa-spinner fa-spin" style="font-size: 2rem; color: #6c757d;"></i>
					<span style="margin-left: 1rem; color: #6c757d;">${this.viewer.localize('loading')}</span>
				</div>
			`
		}
		
		if (!this.previewContent) {
			return html`
				<div class="preview-unsupported">
					<i class="fas fa-exclamation-circle"></i>
					<div>Failed to load content</div>
				</div>
			`
		}
		
		if (this.previewContent === 'unsupported') {
			return html`
				<div class="preview-unsupported">
					<div class="preview-unsupported-container">
						<i class="fa-light fa-eye-slash preview-unsupported-icon"></i>
						<div class="preview-unsupported-title">No preview available for:</div>
						<div class="preview-unsupported-filename">${this.previewFile?.name || ''}</div>

						<lvl-button
							label="Download"
							type="secondary"
							icon="arrow-down-to-line"
							color="info"
							@click=${(e: Event) => { e.stopPropagation(); this.downloadEntry(this.previewFile!) }}
						></lvl-button>
					</div>
				</div>
			`
		}
		
		if (this.previewContent === 'Error loading file') {
			return html`
				<div class="preview-unsupported">
					<i class="fas fa-exclamation-triangle"></i>
					<div>Error loading file</div>
				</div>
			`
		}
		
		if (this.previewContent === 'office') {
			// Render just the Office iframe content without the full MicrosoftViewer UI
			if (!this.officeActionUrl) {
				return html`
					<div style="display: flex; align-items: center; justify-content: center; height: 100%;">
						<i class="fas fa-spinner fa-spin" style="font-size: 2rem; color: #6c757d;"></i>
						<span style="margin-left: 1rem; color: #6c757d;">${this.viewer.localize('loadingOfficeIntegration')}</span>
					</div>`
			}

			// Render just the Office iframe without a toolbar
			return html`
				<div style="width: 100%; height: 100%; position: relative;">
					<iframe
						width="100%"
						height="100%"
						src="${this.officeActionUrl}"
						@mouseenter="${(e: Event) => (e.target as HTMLIFrameElement).focus()}">
					</iframe>
				</div>
			`
		}
		
		if (this.previewContent === 'pdf') {
			// Render PDF using the nested Viewer component
			if (!this.tempFileId) {
				return html`
					<div style="display: flex; align-items: center; justify-content: center; height: 100%;">
						<i class="fas fa-spinner fa-spin" style="font-size: 2rem; color: #6c757d;"></i>
						<span style="margin-left: 1rem; color: #6c757d;">Loading PDF...</span>
					</div>`
			}

			// Render the nested Viewer element with a custom class for identification
			return html`
				<lvl-viewer class="zip-pdf-viewer" style="width: 100%; height: 100%;"></lvl-viewer>
			`
		}
		
		if (!this.previewFile) return ''
		
		const fileExtension = this.previewFile.name.lastIndexOf('.') > -1 ? this.previewFile.name.substring(this.previewFile.name.lastIndexOf('.') + 1).toLowerCase() : ''
		const isImage = ['jpg', 'jpeg', 'png', 'gif', 'webp', 'svg'].includes(fileExtension)
		
		if (isImage) {
			return html`
				<img class="preview-image" 
					 src="${this.previewContent}" 
					 alt="${this.previewFile.name}" 
					 @load=${() => this.applyPreviewZoom()}
					 @wheel=${this.handleImageWheel} />
			`
		} else {
			return html`
				<div class="preview-text" style="font-size: ${this.previewFontSize}px;">${this.previewContent}</div>
			`
		}
	}

	private toggleFolder(folderPath: string) {
		this.collapsedFolders.has(folderPath) ? this.collapsedFolders.delete(folderPath) : this.collapsedFolders.add(folderPath)
		this.collapsedFolders = new Set(this.collapsedFolders)
		this.viewer.requestUpdate()
	}

	private toggleSort = () => {
		this.sortDirection = this.sortDirection === 'asc' ? 'desc' : 'asc'
		this.viewer.requestUpdate()
	}

	private getDirectoryStructure() {
		if (!this.zipContents) return []
		
		const structure: Array<{path: string, name: string, level: number, isDirectory: boolean, uncompressedSize?: number, compressedSize?: number}> = []
		const processedDirs = new Set<string>()
		
		// First, collect all unique directory paths
		for (const entry of this.zipContents.entries) {
			const pathParts = entry.path.split('/').filter(p => p.length > 0)
			
			// Add intermediate directories
			for (let i = 0; i < pathParts.length - 1; i++) {
				const dirPath = pathParts.slice(0, i + 1).join('/')
				if (!processedDirs.has(dirPath)) {
					processedDirs.add(dirPath)
					const dirItem = {
						path: dirPath,
						name: pathParts[i],
						level: i,
						isDirectory: true
					}
					structure.push(dirItem)
				}
			}
			
			// Add the file itself
			const fileItem = {
				path: entry.path,
				name: pathParts[pathParts.length - 1],
				level: pathParts.length - 1,
				isDirectory: false,
				uncompressedSize: entry.uncompressedSize,
				compressedSize: entry.compressedSize,
			}
			structure.push(fileItem)
		}
		
		// Group items by their parent directory for proper hierarchical sorting
		const groupedByParent = new Map<string, typeof structure>()
		
		structure.forEach(item => {
			const pathParts = item.path.split('/')
			const parentPath = pathParts.slice(0, -1).join('/')
			
			if (!groupedByParent.has(parentPath)) {
				groupedByParent.set(parentPath, [])
			}
			groupedByParent.get(parentPath)!.push(item)
		})
		
		// Sort each group separately: directories first, then files
		groupedByParent.forEach((items, parentPath) => {
			const directories = items.filter(item => item.isDirectory)
			const files = items.filter(item => !item.isDirectory)
			
			const sortGroup = (group: typeof items) => group.sort((a, b) => {
				const comparison = a.name.localeCompare(b.name)
				return this.sortDirection === 'asc' ? comparison : -comparison
			})
			
			// Sort directories and files separately, then combine
			const sortedDirectories = sortGroup(directories)
			const sortedFiles = sortGroup(files)
			
			// Replace the original group with a sorted version (directories first)
			const sortedGroup = [...sortedDirectories, ...sortedFiles]
			groupedByParent.set(parentPath, sortedGroup)
		})
		
		// Rebuild the structure maintaining hierarchical order
		const rebuildStructure = (parentPath: string, level: number): typeof structure => {
			const items = groupedByParent.get(parentPath) || []
			const result: typeof structure = []
			
			items.forEach(item => {
				if (item.level === level) {
					result.push(item)
					// Add children of this item
					if (item.isDirectory) {
						result.push(...rebuildStructure(item.path, level + 1))
					}
				}
			})
			
			return result
		}
		
		// Start rebuilding from the root level
		return rebuildStructure('', 0)
	}

	private shouldShowItem(item: any): boolean {
		if (item.level === 0) {
			return true
		}
		
		// Check if any parent directories are collapsed
		const pathParts = item.path.split('/').filter((p: string) => p.length > 0)
		
		// Check all parent directories up to (but not including) the current item's level
		for (let i = 0; i < item.level; i++) {
			const parentPath = pathParts.slice(0, i + 1).join('/')
			if (this.collapsedFolders.has(parentPath)) {
				return false
			}
		}
		
		return true
	}

	private getChildCount(dirPath: string): number {
		if (!this.zipContents) return 0
		let count = 0
		for (const entry of this.zipContents.entries) {
			if (entry.path.startsWith(dirPath + '/')) {
				count++
			}
		}
		return count
	}


	private renderDirectoryStructure() {
		const structure = this.getDirectoryStructure()
		
		return structure.filter(item => this.shouldShowItem(item)).map(item => {
			// Create proper indentation based on level
			// For nested items, we need to align the chevron with the parent's text start
			const indent = Array(item.level).fill(null).map((_, index) => {
				if (index === 0) {
					// First level - use base spacing
					return html`<div class="tree-indent"></div>`
				} else {
					// Nested levels - align chevron with parent folder name
					// Each parent level contributes: icon(1.875rem) + spacing + chevron(1.875rem) + text margin(1rem) = total offset needed
					return html`<div class="tree-indent nested"></div>`
				}
			})
			const isCollapsed = this.collapsedFolders.has(item.path)
			
			return html`
				<div class="tree-item ${item.isDirectory ? 'folder' : 'file'}" @click=${() => item.isDirectory ? this.toggleFolder(item.path) : this.previewEntry({ path: item.path, name: item.name, uncompressedSize: item.uncompressedSize || 0, compressedSize: item.compressedSize || 0, isDirectory: false })}>
					<i class="tree-icon ${item.isDirectory ? 'folder-icon fas fa-folder' : `file-icon ${((filename: string) => {
				const ext = filename.split('.').pop()?.toLowerCase()
				return ext === 'pdf' ? 'far fa-file-pdf' :
					['jpg', 'jpeg', 'png', 'gif', 'webp'].includes(ext || '') ? 'far fa-file-image' :
					['doc', 'docx'].includes(ext || '') ? 'far fa-file-word' :
					['xls', 'xlsx'].includes(ext || '') ? 'far fa-file-excel' :
					['ppt', 'pptx'].includes(ext || '') ? 'far fa-file-powerpoint' :
					ext === 'txt' ? 'far fa-file-lines' :
					['zip', 'rar', '7z', 'tar', 'gz', 'bz2'].includes(ext || '') ? 'far fa-file-zip' : 'far fa-file'
			})(item.name)}`}"></i>

					${indent}
					${item.isDirectory ? html`<div class="tree-indent"></div>` : html`<div class="tree-indent"></div>`}

					${item.isDirectory ? html`
						<i class="fas fa-chevron-right expand-arrow ${!isCollapsed ? 'expanded' : ''}"
						   @click=${(e: Event) => { e.stopPropagation(); this.toggleFolder(item.path) }}></i>
					` : item.level > 0 ? html`
						<div class="expand-spacer"></div>
					` : ''}

					<div class="tree-content">
						<span class="tree-name ${item.isDirectory && !isCollapsed ? 'expanded-folder' : ''}">${item.name}</span>
						<div class="tree-info">
							${item.isDirectory ? html`
								<span class="object-count">${this.getChildCount(item.path)} ${this.getChildCount(item.path) === 1 ? this.viewer.localize('element') : this.viewer.localize('elements')}</span>
							` : html`
								<span class="file-size">${((bytes: number) => {
								if (bytes === 0) return '0 B'
								const k = 1024, sizes = ['B', 'KB', 'MB', 'GB']
								const i = Math.floor(Math.log(bytes) / Math.log(k))
								return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i]
							})(item.uncompressedSize || 0)}</span>
							`}
						</div>
					</div>
				</div>
			`
		})
	}

	private buildZipEntryUrl(entryPath: string): string {
		const baseUrl = `/Api/DataSources/${this.dataSourceId}/Files/${this.fileId}/ZipEntry?entryPath=${encodeURIComponent(entryPath)}`
		return this.zipContents?.isPasswordProtected && this.currentPassword 
			? `${baseUrl}&password=${encodeURIComponent(this.currentPassword)}` 
			: baseUrl
	}

	private async submitPassword(event?: Event) {
		if (event) {
			event.preventDefault()
			event.stopPropagation()
		}
		
		// Use the currentPassword that should be set by the input handler
		const passwordToUse = this.currentPassword || ''
		
		if (!passwordToUse || !passwordToUse.trim()) {
			this.error = this.viewer.localize('pleaseEnterPassword')
			return
		}

		this.passwordLoading = true
		this.error = null
		
		try {
			
			// Call the dedicated decryption API
			const url = `/Api/DataSources/${this.dataSourceId}/Files/${this.fileId}/ZipDecrypt`
			const response = await CommunicationServiceProvider.post<ZipContents>(url, null, {
				body: `"${passwordToUse.trim()}"`,
				headers: {
					'Content-Type': 'application/json'
				}
			})

			if (response.state === CommunicationResponseType.Error) {
				this.error = response.error.errorMessage || 'Invalid password'
				this.viewer.requestUpdate()
			} else if (response.state === CommunicationResponseType.Ok) {
				const data = response.data || { entries: [], isPasswordProtected: false }

				if (data.entries.length > 0) {

					// Clear any previous state and update with decrypted content
					this.error = null
					this.passwordLoading = false
					this.showPasswordForm = false
					this.showPasswordEntry = false
					this.currentPassword = passwordToUse
					this.zipContents = data

					// Update filename from backend response if available
					if (data.fileName && !this.filename) {
						this.filename = data.fileName
					}

					// Initialize collapsed folders for directories with level > 0
					this.initializeCollapsedFolders()

					// Force a re-render to show the decrypted content
					this.viewer.requestUpdate()
				} else {
					this.error = this.viewer.localize('noFilesFoundInEncryptedArchive')
				}
			} else {
				this.error = this.viewer.localize('requestAborted')
			}
		} catch (err) {
			this.error = this.viewer.localize('failedToDecryptZipFile')
			this.viewer.requestUpdate()
		} finally {
			this.passwordLoading = false
		}
	}


	private handlePasswordInput = (e: Event) => {
		// Handle both native input and custom lvl-input events
		const target = e.target as any // lvl-input component
		const detail = (e as CustomEvent).detail
		
		// Get value from different sources depending on event type
		let newValue = ''
		if (e.type === 'input' && detail !== undefined) {
			// Custom lvl-input event - use detail
			newValue = detail || ''
		} else if (e.type === 'change' && detail !== undefined) {
			// Custom lvl-input change event - use detail  
			newValue = detail || ''
		} else if (target.value !== undefined) {
			// Native input or keyup - use target.value
			newValue = target.value || ''
		} else if (target._input?.value !== undefined) {
			// Try to access the internal input element
			newValue = target._input.value || ''
		}
		
		// Only update and re-render if the value actually changed
		if (this.currentPassword !== newValue) {
			this.currentPassword = newValue
			// Force re-render to update button state immediately
			this.viewer.requestUpdate()
		}
	}
	
	private handleEvent = (action: string, e?: Event) => {
		e?.preventDefault()
		e?.stopPropagation()
		
		switch (action) {
			case 'submitPassword':
				this.submitPassword()
				break
			case 'tryAgain':
				Object.assign(this, { error: null, currentPassword: '', passwordLoading: false })
				this.viewer.loadDocument()
				break
			case 'showPasswordForm':
				Object.assign(this, { showPasswordEntry: true, currentPassword: '', error: null })
				this.viewer.requestUpdate()
				break
			case 'backToEncrypted':
				Object.assign(this, { showPasswordEntry: false, currentPassword: '', error: null })
				this.viewer.requestUpdate()
				break
			case 'togglePasswordVisibility':
				this.showPassword = !this.showPassword
				this.viewer.requestUpdate()
				break
		}
	}
	
	private closeAllDropdowns() {
		// Close all dropdown menus by finding all open dropdowns and closing them
		const dropdowns = this.viewer.shadowRoot?.querySelectorAll('lvl-dropdown')
		dropdowns?.forEach(dropdown => {
			// Close the dropdown by calling its close method or setting open to false
			if (dropdown.hasAttribute('open')) {
				dropdown.removeAttribute('open')
			}
		})
	}

	private renderPasswordContent() {
		if (!this.showPasswordForm) return ''
		
		// Show a password entry form
		if (this.showPasswordEntry) {
			return html`
				<div class="password-content">
					<div class="password-form-container">
						<i class="fa-light fa-lock-keyhole password-lock-icon"></i>
						<h3 class="password-title">${this.viewer.localize('enterPasswordHere')}</h3>
						
						<div class="password-input-container">
							<lvl-input
								type="password"
								placeholder="${this.viewer.localize('password')}"
								value=${this.currentPassword}
								@change=${this.handlePasswordInput}
								@input=${this.handlePasswordInput}
								@keyup=${this.handlePasswordInput}
								@keydown=${(e: KeyboardEvent) => {
									if (e.key === 'Enter' && !this.passwordLoading) {
										// Get the current value from the input element directly
										const inputElement = e.target as HTMLInputElement
										const currentValue = inputElement.value?.trim() || ''
										if (currentValue) {
											this.handleEvent('submitPassword')
										}
									}
								}}
								@eye-click=${(e: Event) => this.handleEvent('togglePasswordVisibility', e)}
								?disabled=${this.passwordLoading}
								show-password-toggle
							></lvl-input>
						</div>
						
						${this.error ? html`
							<div class="password-error">
								${this.error}
							</div>
						` : ''}
						
						<div class="password-buttons">
							<lvl-button 
								label="${this.viewer.localize('cancel')}"
								type="secondary"
								color="info"
								@click=${(e: Event) => this.handleEvent('backToEncrypted', e)}
								?disabled=${this.passwordLoading}
							></lvl-button>
							<lvl-button 
								label="${this.passwordLoading ? this.viewer.localize('loading') : this.viewer.localize('enter')}"
								type="primary" 
								@click=${(e: Event) => this.handleEvent('submitPassword', e)}
								?disabled=${this.passwordLoading || !this.currentPassword.trim()}
							></lvl-button>
						</div>
					</div>
				</div>
			`
		}
		
		// Show an encrypted message
		return html`
			<div class="password-content">
				<div class="encrypted-message-container">
					<i class="fa-light fa-lock-keyhole password-lock-icon"></i>
					<div class="encrypted-message-text">${this.viewer.localize('fileHasBeenEncrypted')}</div>
					
					<lvl-button 
						label="${this.viewer.localize('enterPassword')}"
						type="secondary"
						icon="key"
						color="info"
						@click=${(e: Event) => this.handleEvent('showPasswordForm', e)}
					></lvl-button>
				</div>
			</div>
		`
	}

	render() {
		
		if (this.loading) {
			return html`
				<div class="loading">
					<i class="fas fa-spinner fa-spin"></i>
					<span style="margin-left: 0.5rem;">${this.viewer.localize('loadingZipContents')}</span>
				</div>
			`
		}

		if (this.error) {
			
			return html`
				<div class="error">
					<div>
						<i class="fas fa-exclamation-triangle"></i>
						<div style="margin-top: 0.5rem;">${this.error}</div>
						<div style="margin-top: 1rem;">
							<lvl-button 
								label="${this.viewer.localize('tryAgain')}" 
								type="secondary"
								@click=${(e: Event) => this.handleEvent('tryAgain', e)}
							>
							</lvl-button>
						</div>
					</div>
				</div>
			`
		}

		if (!this.zipContents) {
			
			return html`
				<div class="empty">
					<i class="far fa-file-zip" style="font-size: 3rem;"></i>
					<div>${this.viewer.localize('zipFileAppearsEmpty')}</div>
				</div>
			`
		}

		// Check if we should show password-protected content
		if (this.showPasswordForm) {
			return html`
				<div class="zip-container">
					<div class="zip-header">
						<div class="zip-title">
							<span>${this.filename || 'ZIP Archive'}</span>
							<i class="fas fa-lock lock-icon" title="Password protected"></i>
						</div>
						<div class="zip-actions">
							<lvl-button icon="arrow-down-to-line" type="tertiary" @click=${this.downloadEntireZip} tooltip="Download entire ZIP file"></lvl-button>
							<lvl-button icon="${this.sortDirection === 'asc' ? 'arrow-up-a-z' : 'arrow-down-z-a'}" type="tertiary" disabled tooltip="Sort files (available after unlock)"></lvl-button>
						</div>
					</div>
					
					<div class="zip-content">
						${this.renderPasswordContent()}
					</div>
					
				</div>
			`
		}

		
		return html`
			<div class="zip-container">
				<div class="zip-header">
					<div class="zip-title">
						<span>${this.filename || 'ZIP Archive'}</span>
						${this.zipContents.isPasswordProtected ? html`
							<i class="fas fa-lock lock-icon" title="Password protected"></i>
						` : ''}
					</div>
					<div class="zip-actions">
						<lvl-button icon="arrow-down-to-line" type="tertiary" @click=${this.downloadEntireZip} tooltip="Download entire ZIP file"></lvl-button>
						<lvl-button icon="${this.sortDirection === 'asc' ? 'arrow-up-a-z' : 'arrow-down-z-a'}" type="tertiary" @click=${this.toggleSort} tooltip="Sort files by name"></lvl-button>
					</div>
				</div>
				
				<div class="zip-content">
					${this.renderDirectoryStructure()}
				</div>
			</div>
			
			${this.renderPreviewModal()}
		`
	}
}

declare global {
	interface HTMLElementTagNameMap {
		'lvl-zip-viewer': ZipViewer
	}
}