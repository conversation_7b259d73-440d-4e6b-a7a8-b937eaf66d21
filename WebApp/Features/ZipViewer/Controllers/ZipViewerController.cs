using Levelbuild.Core.FrontendDtos;
using Levelbuild.Core.FrontendDtos.ZipViewer;
using Levelbuild.Entities;
using Levelbuild.Frontend.WebApp.Features.LoggerConfig.Interfaces;
using Levelbuild.Frontend.WebApp.Features.Mvc.Frontend;
using Levelbuild.Frontend.WebApp.Features.ZipViewer.Services;
using Levelbuild.Frontend.WebApp.Shared.Services;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;

namespace Levelbuild.Frontend.WebApp.Features.ZipViewer.Controllers;


/// <summary>
/// Controller for ZIP file viewer functionality
/// </summary>
public class ZipViewerController : FrontendController
{
	private readonly CoreDatabaseContext _databaseContext;

	/// <summary>
	/// Controller to manage operations on ZIP files, including browsing archive contents, decrypting files,
	/// and retrieving specific entries from archives.
	/// </summary>
	public ZipViewerController(ILogManager logManager, IDbContextFactory<CoreDatabaseContext> contextFactory, IVersionReader versionReader)
		: base(logManager, logManager.GetLoggerForClass<ZipViewerController>(), versionReader)
	{
		_databaseContext = contextFactory.CreateDbContext();
	}

	#region API Endpoints

	/// <summary>
	/// Retrieves the contents of a ZIP archive, allowing for optional password-protected archives.
	/// </summary>
	/// <param name="dataSourceId">The unique identifier of the data source containing the file.</param>
	/// <param name="fileId">The identifier of the file within the data source to retrieve the ZIP contents from.</param>
	/// <param name="password">An optional password for accessing encrypted ZIP files.</param>
	/// <returns>A task that represents the asynchronous operation. The task result contains an ActionResult wrapping the frontend response with the ZIP contents.</returns>
	[HttpGet("/Api/DataSources/{dataSourceId:guid}/Files/{fileId}/ZipContents")]
	public async Task<ActionResult<FrontendResponse>> GetZipContentsAsync(Guid dataSourceId, string fileId, [FromQuery] string? password = null)
	{
		var contents = await ArchiveHandler.GetArchiveContentsWithPasswordCheckAsync(_databaseContext, dataSourceId, fileId, LogManager, password);
		return GetOkResponse(contents);
	}

	/// <summary>
	/// Decrypts and retrieves the contents of a password-protected ZIP archive.
	/// </summary>
	/// <param name="dataSourceId">The unique identifier of the data source containing the file.</param>
	/// <param name="fileId">The identifier of the file within the data source to decrypt and retrieve the ZIP contents from.</param>
	/// <param name="password">The password required for decrypting the protected ZIP file.</param>
	/// <returns>A task that represents the asynchronous operation. The task result contains an ActionResult wrapping the decrypted ZIP contents in a FrontendResponse.</returns>
	[HttpPost("/Api/DataSources/{dataSourceId:guid}/Files/{fileId}/ZipDecrypt")]
	public async Task<ActionResult<FrontendResponse>> DecryptZipContentsAsync(Guid dataSourceId, string fileId, [FromBody] string password)
	{
		if (string.IsNullOrWhiteSpace(password))
			return GetBadRequestResponse("Password is required for decryption.");

		try
		{
			var contents = await ArchiveHandler.GetArchiveContentsWithPasswordCheckAsync(_databaseContext, dataSourceId, fileId, LogManager, password);
			return GetOkResponse(new ArchiveContentsDto { Entries = contents.Entries, IsPasswordProtected = true, FileName = contents.FileName });
		}
		catch (UnauthorizedAccessException ex)
		{
			Logger.Error("Failed to decrypt ZIP file {FileId}: {ErrorMessage}", fileId, ex.Message);
			return GetBadRequestResponse("Invalid password provided. Please check your password and try again.");
		}
	}

	/// <summary>
	/// Retrieves the content of a specific entry within a ZIP archive, supporting optional password protection.
	/// </summary>
	/// <param name="dataSourceId">The unique identifier of the data source containing the ZIP file.</param>
	/// <param name="fileId">The identifier of the file within the data source to retrieve the ZIP entry content from.</param>
	/// <param name="entryPath">The full path of the specific entry within the ZIP archive to extract.</param>
	/// <param name="password">An optional password required to access encrypted ZIP files.</param>
	/// <returns>A task that represents the asynchronous operation. The task result contains an ActionResult wrapping the content of the extracted ZIP entry or an error message if the operation fails.</returns>
	[HttpGet("/Api/DataSources/{dataSourceId:guid}/Files/{fileId}/ZipEntry")]
	public async Task<ActionResult<object>> GetZipEntryAsync(Guid dataSourceId, string fileId, [FromQuery] string entryPath, [FromQuery] string? password = null)
	{
		if (string.IsNullOrWhiteSpace(entryPath))
			return BadRequest("Entry path is required.");
		
		var context = await ArchiveHandler.GetValidatedArchiveContextAsync(_databaseContext, dataSourceId, fileId, password, entryPath);
		return await ArchiveHandler.ExtractArchiveEntryAsync(context!);
	}

	/// <summary>
	/// Extracts an entry from a ZIP archive using a temporary file identifier and optional password.
	/// </summary>
	/// <param name="dataSourceId">The unique identifier of the data source containing the archive.</param>
	/// <param name="tempFileId">The temporary file identifier, which includes the original file ID and the encoded entry path.</param>
	/// <param name="password">An optional password required for accessing encrypted archives.</param>
	/// <returns>A task that represents the asynchronous operation. The task result contains an ActionResult wrapping the extracted ZIP entry object.</returns>
	[HttpGet("/Api/DataSources/{dataSourceId:guid}/Files/{tempFileId}/ZipEntryTemp")]
	public async Task<ActionResult<object>> GetZipEntryTempAsync(Guid dataSourceId, string tempFileId, [FromQuery] string? password = null)
	{
		if (!tempFileId.Contains("_zipentry_") || tempFileId.Split(["_zipentry_"], StringSplitOptions.None).Length != 2)
			return BadRequest("Invalid temporary file ID format.");
		
		var parts = tempFileId.Split(["_zipentry_"], StringSplitOptions.None);
		var entryPath = Uri.UnescapeDataString(parts[1]);
		
		var context = await ArchiveHandler.GetValidatedArchiveContextAsync(_databaseContext, dataSourceId, parts[0], password, entryPath);
		return await ArchiveHandler.ExtractArchiveEntryAsync(context!);
	}

	/// <summary>
	/// Downloads a specific entry from a ZIP archive with forced download headers.
	/// </summary>
	/// <param name="dataSourceId">The unique identifier of the data source containing the ZIP file.</param>
	/// <param name="fileId">The identifier of the file within the data source to retrieve the ZIP entry from.</param>
	/// <param name="entryPath">The full path of the specific entry within the ZIP archive to download.</param>
	/// <param name="password">An optional password required to access encrypted ZIP files.</param>
	/// <returns>A task that represents the asynchronous operation. The task result contains an ActionResult wrapping the downloadable ZIP entry content.</returns>
	[HttpGet("/Api/DataSources/{dataSourceId:guid}/Files/{fileId}/ZipEntryDownload")]
	public async Task<ActionResult> DownloadZipEntryAsync(Guid dataSourceId, string fileId, [FromQuery] string entryPath, [FromQuery] string? password = null)
	{
		if (string.IsNullOrWhiteSpace(entryPath))
			return BadRequest("Entry path is required.");
		
		var context = await ArchiveHandler.GetValidatedArchiveContextAsync(_databaseContext, dataSourceId, fileId, password, entryPath);
		var result = await ArchiveHandler.ExtractArchiveEntryAsync(context!);
		
		// Ensure the result is a FileContentResult and explicitly set download headers
		if (result.Value is FileContentResult fileResult)
		{
			Response.Headers.Append("Content-Disposition", $"attachment; filename=\"{fileResult.FileDownloadName}\"");
			return fileResult;
		}
		
		return result.Result ?? BadRequest("Failed to extract archive entry.");
	}

	/// <summary>
	/// Downloads the entire ZIP archive file.
	/// </summary>
	/// <param name="dataSourceId">The unique identifier of the data source containing the ZIP file.</param>
	/// <param name="fileId">The identifier of the ZIP file to download.</param>
	/// <returns>A task that represents the asynchronous operation. The task result contains an ActionResult wrapping the downloadable ZIP file.</returns>
	[HttpGet("/Api/DataSources/{dataSourceId:guid}/Files/{fileId}/Download")]
	public async Task<ActionResult> DownloadZipFileAsync(Guid dataSourceId, string fileId)
	{
			var result = await ArchiveHandler.GetArchiveFileAsync(_databaseContext, dataSourceId, fileId);
		
			// Ensure the result is a FileContentResult and explicitly set download headers
			if (result is not FileContentResult fileResult) return result;
			Response.Headers.Append("Content-Disposition", $"attachment; filename=\"{fileResult.FileDownloadName}\"");
			return fileResult;
	}
	
	#endregion
}